import { useEffect, useState } from "react";
import { Upload, Save, X, Check } from "lucide-react";
import { HiAdjustments } from "react-icons/hi";
import { useNavigate } from "react-router-dom";
import Cookies from "js-cookie";
import toast from "react-hot-toast";

const GeneralProfile = () => {
  const navigate = useNavigate();
  const emp_id = Cookies.get("employerId");

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editActive, setEditActive] = useState(false);
  const [empName, setEmpName] = useState("");
  const [companyUrl, setCompanyUrl] = useState("");
  const [companyLogo, setCompanyLogo] = useState(null);
  const [companyLogoPreview, setCompanyLogoPreview] = useState(null);
  const [twitterHandle, setTwitterHandle] = useState("");
  const [facebookHandle, setFacebookHandle] = useState("");
  const [linkedinHandle, setLinkedinHandle] = useState("");
  const [includeInReports, setIncludeInReports] = useState(true);
  const [includeInEmails, setIncludeInEmails] = useState(true);

  // Store original values for cancel functionality
  const [originalValues, setOriginalValues] = useState({
    empName: "",
    companyUrl: "",
    companyLogo: null
  });

  // Handle file upload for company logo
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type and size
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'];
      const maxSize = 2 * 1024 * 1024; // 2MB

      if (!validTypes.includes(file.type)) {
        toast.error("Please upload a valid image file (JPG, PNG, GIF, SVG)");
        return;
      }

      if (file.size > maxSize) {
        toast.error("File size must be less than 2MB");
        return;
      }

      setCompanyLogo(file);
      const previewUrl = URL.createObjectURL(file);
      setCompanyLogoPreview(previewUrl);
      toast.success("Logo selected successfully");
    }
  };

  // Handle drag and drop for logo upload
  const handleLogoDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) {
      const event = { target: { files: [file] } };
      handleLogoUpload(event);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  // Save profile changes
  const handleSave = async () => {
    if (!empName.trim()) {
      toast.error("Company name is required");
      return;
    }

    if (!companyUrl.trim()) {
      toast.error("Company URL is required");
      return;
    }

    setSaving(true);
    try {
      const formData = new FormData();
      formData.append("emp_name", empName.trim());
      formData.append("company_website", companyUrl.trim());

      if (companyLogo) {
        formData.append("company_logo", companyLogo);
      }

      const response = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/updateEmployerProfile`,
        {
          method: "PUT",
          headers: {
            Authorization: emp_id,
          },
          body: formData,
        }
      );

      const result = await response.json();

      if (response.ok) {
        toast.success("Profile updated successfully!");
        setEditActive(false);
        // Update original values
        setOriginalValues({
          empName,
          companyUrl,
          companyLogo: companyLogoPreview
        });
        // Refresh profile data
        await getProfileData();
      } else {
        toast.error(result.message || "Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Cancel editing and revert changes
  const handleCancel = () => {
    setEmpName(originalValues.empName);
    setCompanyUrl(originalValues.companyUrl);
    setCompanyLogo(null);
    setCompanyLogoPreview(originalValues.companyLogo);
    setEditActive(false);
    toast.success("Changes cancelled");
  };

  // Start editing mode
  const handleEdit = () => {
    setOriginalValues({
      empName,
      companyUrl,
      companyLogo: companyLogoPreview
    });
    setEditActive(true);
  };

  const getProfileData = async () => {
    setLoading(true);
    try {
      const profileReq = await fetch(
        `${import.meta.env.VITE_APP_HOST}/api/v1/getEmployerDetails`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: emp_id,
          },
        }
      );
      const profileJson = await profileReq.json();
      
      const profileData = profileJson.data || {};
      console.log(profileData);
      if (profileData) {
        setEmpName(profileData.emp_name || "");
        setCompanyUrl(profileData.company_website || "");
        setTwitterHandle(profileData?.company_twitterHandle || "");
        setFacebookHandle(profileData?.company_facebookHandle || "");
        setLinkedinHandle(profileData?.company_linkedinHandle || "");

        // Set company logo if available
        if (profileData.company_logo) {
          const logoUrl = `${import.meta.env.VITE_APP_HOST}/${profileData.company_logo}`;
          setCompanyLogoPreview(logoUrl);
        }

        // Set original values
        setOriginalValues({
          empName: profileData.emp_name || "",
          companyUrl: profileData.company_website || "",
          companyLogo: profileData.company_logo ? `${import.meta.env.VITE_APP_HOST}/${profileData.company_logo}` : null
        });
      }
    } catch (err) {
      console.error("Error fetching profile data:", err);
      toast.error("Failed to load profile data. Please refresh the page.");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (emp_id) {
      getProfileData();
    } else {
      navigate("/candidate/signIn");
    }
  }, []);

  return (
    <div className="p-6">
      {/* Header Section */}
      <div className="mb-8 flex items-center gap-3">
        <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
          <HiAdjustments className="w-4 h-4 text-white" />
        </div>
        <h2 className="text-xl font-bold text-gray-900 dark:text-white">General Settings</h2>
      </div>
      {loading ? (
        <div className="flex items-center justify-center py-16">
          <div className="flex items-center space-x-3">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
            <span className="text-sm text-gray-600 dark:text-gray-400">Loading profile...</span>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Company Profile Card */}
          <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-navy-700 dark:to-navy-800 rounded-xl border border-gray-200 dark:border-navy-600 p-6 shadow-sm">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg">
                  <span className="text-lg font-bold">
                    {empName ? empName.slice(0, 2).toUpperCase() : ""}
                  </span>
                </div>
                <div>
                  <h1 className="text-xl font-bold text-gray-900 dark:text-white">{empName}</h1>
                  <a
                    href={`https://untitledui.com/${companyUrl}`}
                    className="text-blue-600 dark:text-blue-400 hover:underline text-sm"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    untitledui.com/{companyUrl}
                  </a>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-3">
                {editActive ? (
                  <>
                    <button
                      onClick={handleCancel}
                      disabled={saving}
                      className="px-4 py-2 bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2"
                    >
                      <X className="w-4 h-4" />
                      Cancel
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={saving}
                      className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 disabled:from-green-400 disabled:to-emerald-400 text-white rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md flex items-center gap-2"
                    >
                      {saving ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="w-4 h-4" />
                          Save Changes
                        </>
                      )}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleEdit}
                    className="px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-sm hover:shadow-md"
                  >
                    Edit Profile
                  </button>
                )}
              </div>
            </div>

            <div className="border-t border-gray-200 dark:border-navy-600 pt-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Company Profile</h2>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                Update your company photo and details here.
              </p>

              {/* Public Profile Section */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                    Public Profile
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                    This will be displayed on your profile.
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Company Name
                    </label>
                    <input
                      type="text"
                      value={empName}
                      onChange={(e) => setEmpName(e.target.value)}
                      className={`w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
                        !editActive ? "bg-gray-50 dark:bg-gray-800 cursor-not-allowed" : ""
                      }`}
                      placeholder="Enter company name"
                      disabled={!editActive}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Company URL
                    </label>
                    <div className="flex">
                      <span className="inline-flex items-center px-3 rounded-l-lg border border-r-0 border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 text-sm text-gray-500 dark:text-gray-400">
                        untitledui.com/
                      </span>
                      <input
                        type="text"
                        value={companyUrl}
                        onChange={(e) => setCompanyUrl(e.target.value)}
                        className={`flex-1 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-r-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200 ${
                          !editActive ? "bg-gray-50 dark:bg-gray-800 cursor-not-allowed" : ""
                        }`}
                        placeholder="company-url"
                        disabled={!editActive}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Company Logo Section */}
          <div className="bg-gradient-to-br from-gray-50 to-gray-100 dark:from-navy-700 dark:to-navy-800 rounded-xl border border-gray-200 dark:border-navy-600 p-6 shadow-sm">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Company Logo
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                  Update your company logo and choose where to display it.
                </p>
                <div className="flex justify-center lg:justify-start">
                  <div className="relative">
                    {companyLogoPreview ? (
                      <div className="relative">
                        <img
                          src={companyLogoPreview}
                          alt="Company Logo"
                          className="h-20 w-20 object-cover rounded-xl shadow-lg border-2 border-gray-200 dark:border-gray-600"
                        />
                        {editActive && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 rounded-xl flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200">
                            <span className="text-white text-xs font-medium">Change</span>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="flex h-20 w-20 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg">
                        <svg
                          className="h-10 w-10 text-white"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"></path>
                        </svg>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div>
                <div
                  className={`relative cursor-pointer rounded-xl border-2 border-dashed border-gray-300 dark:border-gray-600 p-8 text-center transition-all duration-200 hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/10 ${
                    !editActive ? "pointer-events-none cursor-not-allowed opacity-50" : ""
                  }`}
                  onDrop={handleLogoDrop}
                  onDragOver={handleDragOver}
                  onClick={() => editActive && document.getElementById('logo-upload').click()}
                >
                  <Upload className="mx-auto h-8 w-8 text-gray-400 mb-4" />
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    <span className="font-medium text-blue-600 dark:text-blue-400 hover:underline">
                      Click to upload
                    </span>{" "}
                    or drag and drop
                  </p>
                  <p className="mt-2 text-xs text-gray-500 dark:text-gray-500">
                    SVG, PNG, JPG or GIF (max. 2MB)
                  </p>
                  <input
                    id="logo-upload"
                    type="file"
                    accept="image/*"
                    onChange={handleLogoUpload}
                    className="hidden"
                    disabled={!editActive}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GeneralProfile;
